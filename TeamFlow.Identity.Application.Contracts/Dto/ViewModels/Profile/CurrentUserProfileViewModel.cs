using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Users;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Profile;

public record CurrentUserProfileViewModel
{
    public Guid Id { get; init; }
    public string FirstName { get; init; }
    public string LastName { get; init; }
    public string Login { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string? AvatarUrl { get; init; } = null;
    public UserOnlineStatus OnlineStatus { get; init; }
    public UserRole Role { get; init; }
    public DateTime? LastLoginAt { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public Guid PositionId { get; init; }
    public string PositionName { get; init; } = string.Empty;
    public List<UserSkillViewModel>? Skills { get; init; } = [];
    public UserSettingsViewModel? Settings { get; init; }
    
    //TODO AuthEvent'ы. Сессии
}