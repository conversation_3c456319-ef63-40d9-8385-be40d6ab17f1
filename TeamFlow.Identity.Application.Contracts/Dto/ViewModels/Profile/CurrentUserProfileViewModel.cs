using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Users;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Sessions;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels.AuthEvents;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Profile;

public record CurrentUserProfileViewModel
{
    public required Guid Id { get; init; }
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required string Login { get; init; } = string.Empty;
    public required string Email { get; init; } = string.Empty;
    public required UserOnlineStatus OnlineStatus { get; init; }
    public required UserRole Role { get; init; }
    public required Guid PositionId { get; init; }
    public required string PositionName { get; init; } = string.Empty;
    public string? AvatarUrl { get; init; } = null;
    public DateTime? LastLoginAt { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public List<UserSkillViewModel>? Skills { get; init; } = [];
    public UserSettingsViewModel? Settings { get; init; }
    public List<UserSessionViewModel> Sessions { get; init; } = [];
    public List<AuthEventViewModel> RecentAuthEvents { get; init; } = [];
}