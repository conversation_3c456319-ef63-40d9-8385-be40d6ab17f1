using TeamFlow.Identity.Core.Enums.Users;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels;

public record UserViewModel
{
    public Guid Id { get; init; }
    public string Login { get; init; } = string.Empty;
    public string FirstName { get; init; } = string.Empty;
    public string LastName { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string? AvatarUrl { get; init; }
    public UserOnlineStatus OnlineStatus { get; init; }
    public UserRole Role { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public Guid PositionId { get; init; }
    public List<Guid> SkillIds { get; init; } = [];
}