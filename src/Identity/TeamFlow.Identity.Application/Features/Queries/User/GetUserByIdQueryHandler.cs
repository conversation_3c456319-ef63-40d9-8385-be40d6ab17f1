using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Queries.User;

public class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, Result<UserViewModel>>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IValidator<GetUserByIdQuery> _validator;
    private readonly ILogger<GetUserByIdQueryHandler> _logger;

    public GetUserByIdQueryHandler(IGenericRepository<Core.Entities.User, Guid> usersRepository,
        IValidator<GetUserByIdQuery> validator, ILogger<GetUserByIdQueryHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<UserViewModel>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }

        var user = await _usersRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        return user is null
            ? Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.Id))
            : Result.Ok(user.ToViewModel());
    }
}