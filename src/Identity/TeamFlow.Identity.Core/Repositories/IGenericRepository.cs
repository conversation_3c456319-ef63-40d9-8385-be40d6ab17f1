using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Shared.Repositories.Repositories.Common;

namespace TeamFlow.Identity.Core.Repositories;

public interface IGenericRepository<TEntity, in TKey> 
    : IGetOne<TEntity>, IGetMany<TEntity>, IAdd<TEntity>, IUpdate<TEntity>,
    IRemove<TEntity>, IFindAny<TEntity>, IBulkOperations<TEntity, TKey>, ITransactional
    where TEntity : class, IEntity<TKey> where TKey : IEquatable<TKey>;